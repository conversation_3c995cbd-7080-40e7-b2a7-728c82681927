const readline = require('readline');
const fs = require('fs');
const { organizeWordDocuments } = require('./organize-word-docs');

// 创建readline接口
const rl = readline.createInterface({
    input: process.stdin,
    output: process.stdout
});

// 询问用户文件夹路径
function askForFolderPath() {
    return new Promise((resolve) => {
        const defaultPath = '/Users/<USER>/Downloads/高中数学/2026版 步步高 大一轮 数学 人教A版 提高版（教师用书）-【123份】';
        
        console.log('Word文档组织工具');
        console.log('='.repeat(50));
        console.log(`默认文件夹路径: ${defaultPath}`);
        console.log('');
        
        rl.question('请输入Word文档所在的文件夹路径 (直接回车使用默认路径): ', (answer) => {
            const folderPath = answer.trim() || defaultPath;
            resolve(folderPath);
        });
    });
}

// 确认操作
function confirmOperation(folderPath) {
    return new Promise((resolve) => {
        console.log(`\n即将处理文件夹: ${folderPath}`);
        console.log('注意: 此操作将重命名文件夹中的Word文档！');
        console.log('建议在操作前备份文件。');
        console.log('');
        
        rl.question('确认继续吗？(y/N): ', (answer) => {
            const confirmed = answer.toLowerCase() === 'y' || answer.toLowerCase() === 'yes';
            resolve(confirmed);
        });
    });
}

// 主函数
async function main() {
    try {
        // 获取文件夹路径
        const folderPath = await askForFolderPath();
        
        // 检查文件夹是否存在
        if (!fs.existsSync(folderPath)) {
            console.log(`\n错误: 文件夹不存在: ${folderPath}`);
            rl.close();
            return;
        }
        
        // 确认操作
        const confirmed = await confirmOperation(folderPath);
        if (!confirmed) {
            console.log('\n操作已取消。');
            rl.close();
            return;
        }
        
        console.log('\n开始处理...\n');
        
        // 临时修改organize-word-docs.js中的路径
        const organizeScript = fs.readFileSync('./organize-word-docs.js', 'utf8');
        const modifiedScript = organizeScript.replace(
            /const FOLDER_PATH = '.*?';/,
            `const FOLDER_PATH = '${folderPath}';`
        );
        fs.writeFileSync('./organize-word-docs.js', modifiedScript);
        
        // 运行组织脚本
        await organizeWordDocuments();
        
        console.log('\n处理完成！');
        
    } catch (error) {
        console.error('\n发生错误:', error.message);
    } finally {
        rl.close();
    }
}

// 运行主函数
if (require.main === module) {
    main();
}
