# DOCX 文件上传工具

这个工具可以批量上传 docx 文件到阿里云 OSS，并生成 ID 和文件名的映射关系。

## 功能特性

- 🔍 自动扫描目录中的所有 docx 文件
- 🔐 计算每个文件的 MD5 值
- 📤 使用 multipart 方式上传到阿里云 OSS
- 🆔 为每个文件生成唯一的 UUID 作为存储键名
- 💾 维护 key-value 存储映射关系
- 📊 生成详细的上传报告

## 安装依赖

```bash
npm install ali-oss crypto uuid fs-extra path
```

## 使用方法

### 基本用法

```bash
# 上传当前目录下的所有 docx 文件
node index.js ./

# 指定输入目录
node index.js /path/to/your/docs

# 指定输入目录和输出文件
node index.js /path/to/your/docs ./output/mapping.json
```

### 编程方式使用

```javascript
const { main, processDocxFile } = require('./index.js');

// 批量处理
await main('./docs', './mapping.json');

// 处理单个文件
const result = await processDocxFile('./document.docx');
console.log(result);
// 输出: { id: 'uuid', wordName: 'document', md5: 'hash', originalPath: './document.docx' }
```

## 输出文件

工具会生成两个 JSON 文件：

### 1. 详细映射文件 (mapping.json)
```json
{
  "timestamp": "2024-01-01T00:00:00.000Z",
  "totalFiles": 2,
  "mappings": [
    {
      "id": "d002c34aa5a4b2df370f0784f743a71b",
      "wordName": "document1",
      "md5": "5d41402abc4b2a76b9719d911017c592",
      "originalPath": "./docs/document1.docx"
    }
  ]
}
```

### 2. 简化映射文件 (mapping_simple.json)
```json
{
  "d002c34aa5a4b2df370f0784f743a71b": "document1",
  "e003d45bb6b5c3ef481f1895g854b82c": "document2"
}
```

## 配置说明

OSS 配置信息在代码中设置：

```javascript
const ossInfo = {
  region: 'oss-cn-shanghai',
  accessKeyId: 'your-access-key-id',
  accessKeySecret: 'your-access-key-secret',
  bucket: 'your-bucket-name',
};
```

## 测试

运行测试脚本验证功能：

```bash
node test.js
```

## 注意事项

1. 确保 OSS 配置信息正确
2. 确保有足够的网络带宽进行上传
3. 大文件上传可能需要较长时间
4. 上传失败的文件会在控制台显示错误信息
5. 生成的 UUID 格式为 32 位十六进制字符串（无连字符）

## 错误处理

- 文件读取失败：检查文件权限和路径
- 上传失败：检查网络连接和 OSS 配置
- MD5 计算失败：检查文件是否损坏

## API 参考

### calculateMD5(filePath)
计算文件的 MD5 值

### uploadFileWithMultipart(filePath, objectKey)
使用 multipart 方式上传文件到 OSS

### processDocxFile(filePath)
处理单个 docx 文件（计算 MD5、生成 UUID、上传）

### findDocxFiles(directory)
递归扫描目录中的所有 docx 文件

### saveMapping(mappings, outputFile)
保存映射关系到 JSON 文件
