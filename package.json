{"name": "word-document-organizer", "version": "1.0.0", "description": "Organize Word documents based on table of contents", "main": "organize-word-docs.js", "scripts": {"start": "node run-organizer.js", "organize": "node organize-word-docs.js", "test": "node test-organizer.js"}, "dependencies": {"mammoth": "^1.6.0"}, "keywords": ["word", "document", "organizer", "table-of-contents"], "author": "", "license": "MIT"}