{"name": "word-document-organizer", "version": "1.0.0", "description": "Organize Word documents based on table of contents", "main": "organize-word-docs.js", "scripts": {"start": "node run-organizer.js", "organize": "node organize-word-docs.js", "test": "node test-organizer.js"}, "dependencies": {"ali-oss": "^6.23.0", "crypto": "^1.0.1", "fs-extra": "^11.3.0", "mammoth": "^1.6.0", "path": "^0.12.7", "uuid": "^11.1.0"}, "keywords": ["word", "document", "organizer", "table-of-contents"], "author": "", "license": "MIT"}