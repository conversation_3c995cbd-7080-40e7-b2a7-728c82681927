{"name": "is-type-of", "version": "1.4.0", "description": "complete type checking for node", "main": "index.js", "scripts": {"test": "tsd && egg-bin test", "ci": "tsd && egg-bin cov", "lint": "echo 'ignore'"}, "repository": {"type": "git", "url": "git://github.com/node-modules/is-type-of.git"}, "files": ["index.js", "index.d.ts"], "keywords": ["typeof", "checker", "type", "is"], "author": "dead_horse <<EMAIL>>", "license": "MIT", "bugs": {"url": "https://github.com/node-modules/is-type-of/issues"}, "homepage": "https://github.com/node-modules/is-type-of", "dependencies": {"core-util-is": "^1.0.2", "is-class-hotfix": "~0.0.6", "isstream": "~0.1.2"}, "devDependencies": {"@types/node": "^18.16.3", "beautify-benchmark": "^0.2.4", "benchmark": "^2.1.4", "egg-bin": "^6.4.0", "long": "^3.2.0", "semver": "^5.4.1", "tsd": "^0.28.1"}}