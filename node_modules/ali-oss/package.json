{"name": "ali-oss", "version": "6.23.0", "description": "aliyun oss(object storage service) node client", "main": "./lib/client.js", "files": ["lib", "shims", "dist"], "browser": {"./lib/client.js": "./dist/aliyun-oss-sdk.js", "mime": "mime/lite", "urllib": "./shims/xhr.js", "utility": "./shims/utility.js", "crypto": "./shims/crypto/crypto.js", "debug": "./shims/debug", "fs": false, "child_process": false, "is-type-of": "./shims/is-type-of.js"}, "scripts": {"build-change-log": "standard-version", "test": "npm run tsc && mocha -t 120000 -r should -r dotenv/config test/node/*.test.js test/node/**/*.test.js", "test-cov": "npm run tsc && nyc --reporter=lcov node_modules/.bin/_mocha -t 120000 -r should test/node/*.test.js test/node/**/*.test.js", "jshint": "jshint .", "build-test": "mkdir -p ./test/browser/build && MINIFY=1 node browser-build.js > test/browser/build/aliyun-oss-sdk.min.js && node -r dotenv/config task/browser-test-build.js > test/browser/build/tests.js", "browser-test": "npm run build-test && karma start", "build-dist": "npm run tsc && node browser-build.js > dist/aliyun-oss-sdk.js && MINIFY=1 node browser-build.js > dist/aliyun-oss-sdk.min.js", "publish-to-npm": "node publish-npm-check.js && npm publish", "publish-to-cdn": "node publish.js", "snyk-protect": "snyk-protect", "lint-staged": "lint-staged", "detect-secrets": "node task/detect-secrets", "tsc": "npm run tsc:clean && npm run tsc:build", "tsc:build": "tsc -b tsconfig.json tsconfig-cjs.json", "tsc:watch": "tsc -b tsconfig.json tsconfig-cjs.json --watch", "tsc:clean": "tsc -b tsconfig.json tsconfig-cjs.json --clean ", "prepare": "husky install"}, "git-pre-hooks": {"pre-release": "npm run build-dist", "post-release": ["npm run publish-to-npm", "npm run publish-to-cdn"]}, "homepage": "https://github.com/ali-sdk/ali-oss", "bugs": {"url": "https://github.com/ali-sdk/ali-oss/issues"}, "publishConfig": {"registry": "https://registry.npmjs.org/", "access": "public"}, "repository": {"type": "git", "url": "https://github.com/ali-sdk/ali-oss.git"}, "keywords": ["oss", "client", "file", "<PERSON><PERSON><PERSON>"], "author": "dead_horse", "license": "MIT", "engines": {"node": ">=8"}, "devDependencies": {"@alicloud/openapi-client": "^0.4.10", "@alicloud/resourcemanager20200331": "^2.3.0", "@alicloud/tea-util": "^1.4.9", "@babel/core": "^7.11.6", "@babel/plugin-transform-regenerator": "^7.10.4", "@babel/plugin-transform-runtime": "^7.11.5", "@babel/preset-env": "^7.11.5", "@babel/runtime": "^7.11.2", "@commitlint/cli": "^17.6.7", "@commitlint/config-conventional": "^16.2.4", "@octokit/core": "^5.0.0", "@semantic-release/exec": "^6.0.3", "@semantic-release/git": "^10.0.1", "@semantic-release/npm": "^10.0.5", "@snyk/protect": "^1.1196.0", "@types/node": "^14.0.12", "@typescript-eslint/eslint-plugin": "^5.0.0", "@typescript-eslint/parser": "^5.0.0", "aliasify": "^2.0.0", "axios": "^0.27.2", "babelify": "^10.0.0", "beautify-benchmark": "^0.2.4", "benchmark": "^2.1.1", "bluebird": "^3.1.5", "browserify": "^17.0.0", "core-js": "^3.6.5", "crypto-js": "^4.2.0", "dotenv": "^8.2.0", "eslint": "^8.44.0", "eslint-config-airbnb": "^19.0.4", "eslint-config-ali": "^13.0.0", "eslint-config-prettier": "^8.8.0", "eslint-plugin-import": "^2.21.1", "eslint-plugin-jsx-a11y": "^6.0.3", "eslint-plugin-prettier": "^4.2.1", "filereader": "^0.10.3", "form-data": "^4.0.0", "git-pre-hooks": "^1.2.0", "husky": "^7.0.4", "immediate": "^3.3.0", "karma": "^6.3.4", "karma-browserify": "^8.1.0", "karma-chrome-launcher": "^2.2.0", "karma-firefox-launcher": "^1.0.1", "karma-ie-launcher": "^1.0.0", "karma-mocha": "^2.0.1", "karma-safari-launcher": "^1.0.0", "lint-staged": "^12.4.1", "mm": "^2.0.0", "mocha": "^9.1.2", "nise": "5.1.4", "nyc": "^15.1.0", "prettier": "^3.0.0", "promise-polyfill": "^6.0.2", "puppeteer": "19.0.0", "semantic-release": "^21.1.1", "should": "^11.0.0", "sinon": "^15.2.0", "standard-version": "^9.3.1", "stream-equal": "^1.1.0", "timemachine": "^0.3.0", "typescript": "^3.9.5", "uglify-js": "^3.14.2", "watchify": "^4.0.0"}, "dependencies": {"address": "^1.2.2", "agentkeepalive": "^3.4.1", "bowser": "^1.6.0", "copy-to": "^2.0.1", "dateformat": "^2.0.0", "debug": "^4.3.4", "destroy": "^1.0.4", "end-or-error": "^1.0.1", "get-ready": "^1.0.0", "humanize-ms": "^1.2.0", "is-type-of": "^1.4.0", "js-base64": "^2.5.2", "jstoxml": "^2.0.0", "lodash": "^4.17.21", "merge-descriptors": "^1.0.1", "mime": "^2.4.5", "platform": "^1.3.1", "pump": "^3.0.0", "qs": "^6.4.0", "sdk-base": "^2.0.1", "stream-http": "2.8.2", "stream-wormhole": "^1.0.4", "urllib": "^2.44.0", "utility": "^1.18.0", "xml2js": "^0.6.2"}, "snyk": true, "lint-staged": {"**/!(dist)/*": ["npm run detect-secrets --"], "**/*.{js,ts}": ["eslint --cache --fix --ext .js,.ts", "prettier --write", "git add"]}}