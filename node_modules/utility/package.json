{"name": "utility", "version": "1.18.0", "description": "A collection of useful utilities.", "main": "utility.js", "files": ["*.js", "index.d.ts"], "scripts": {"test": "npm run lint && npm run test-local", "test-ts": "npm run test-local-ts", "test-local": "ava test/**/*.test.js", "test-local-ts": "ava-ts test_ts/**/*.test.ts", "test-cov": "nyc ava test/**/*.test.js && nyc report --reporter=lcov", "lint": "jshint .", "ci": "npm run lint && npm run test-cov && npm run test-ts", "test-optimized": "node --allow-natives-syntax --trace_opt --trace_deopt test/optimized.js", "contributor": "git-contributor"}, "dependencies": {"copy-to": "^2.0.1", "escape-html": "^1.0.3", "mkdirp": "^0.5.1", "mz": "^2.7.0", "unescape": "^1.0.1"}, "devDependencies": {"@types/escape-html": "0.0.20", "@types/node": "^10.12.12", "ava": "^0.25.0", "ava-ts": "^0.25.2", "beautify-benchmark": "*", "benchmark": "^2.1.0", "contributors": "*", "git-contributor": "^1.0.10", "jshint": "*", "moment": "^2.22.2", "nyc": "6", "object-assign": "^4.1.1", "optimized": "^1.2.0", "rimraf": "^2.6.2", "time-require": "^0.1.2", "ts-node": "^7.0.1", "typescript": "^5.0.4"}, "homepage": "https://github.com/node-modules/utility", "repository": {"type": "git", "url": "git://github.com/node-modules/utility.git", "web": "https://github.com/node-modules/utility"}, "keywords": ["utility", "util", "utils", "sha256", "sha1", "hash", "hex"], "engines": {"node": ">= 0.12.0"}, "author": "fengmk2 <<EMAIL>> (https://github.com/fengmk2)", "license": "MIT"}