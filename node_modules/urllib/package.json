{"name": "urllib", "version": "2.44.0", "publishConfig": {"tag": "latest-2"}, "description": "Help in opening URLs (mostly HTTP) in a complex world — basic and digest authentication, redirections, cookies and more.", "keywords": ["urllib", "http", "urlopen", "curl", "wget", "request", "https"], "author": "fengmk2 <<EMAIL>> (https://fengmk2.com)", "homepage": "https://github.com/node-modules/urllib", "main": "lib/index.js", "types": "lib/index.d.ts", "files": ["lib"], "repository": {"type": "git", "url": "git://github.com/node-modules/urllib.git"}, "scripts": {"tsd": "node test/tsd.js", "test-local": "mocha -t 30000 test/*.test.js", "test": "npm run lint && npm run test-local", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- -t 30000 test/*.test.js", "ci": "npm run lint && npm run tsd && npm run test-cov", "lint": "jshint .", "contributor": "git-contributor"}, "dependencies": {"any-promise": "^1.3.0", "content-type": "^1.0.2", "default-user-agent": "^1.0.0", "digest-header": "^1.0.0", "ee-first": "~1.1.1", "formstream": "^1.1.0", "humanize-ms": "^1.2.0", "iconv-lite": "^0.6.3", "pump": "^3.0.0", "qs": "^6.4.0", "statuses": "^1.3.1", "utility": "^1.16.1"}, "peerDependencies": {"proxy-agent": "^5.0.0"}, "peerDependenciesMeta": {"proxy-agent": {"optional": true}}, "devDependencies": {"@types/mocha": "^5.2.5", "@types/node": "^10.12.18", "agentkeepalive": "^4.0.0", "benchmark": "^2.1.4", "bluebird": "*", "busboy": "^0.2.14", "co": "*", "coffee": "1", "git-contributor": "2", "http-proxy": "^1.16.2", "istanbul": "*", "jshint": "*", "mkdirp": "^0.5.1", "mocha": "3", "muk": "^0.5.3", "pedding": "^1.1.0", "proxy-agent": "^5.0.0", "semver": "5", "spy": "^1.0.0", "tar": "^4.4.8", "through2": "^2.0.3", "tsd": "^0.18.0", "typescript": "^4.4.4"}, "engines": {"node": ">= 0.10.0"}, "license": "MIT"}