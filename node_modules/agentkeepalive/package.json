{"name": "agentkeepalive", "version": "3.5.3", "description": "Missing keepalive http.Agent", "main": "index.js", "browser": "browser.js", "files": ["index.js", "index.d.ts", "browser.js", "lib"], "scripts": {"test": "egg-bin test", "cov": "egg-bin cov", "ci": "npm run lint && npm run cov", "lint": "eslint lib test index.js"}, "repository": {"type": "git", "url": "git://github.com/node-modules/agentkeepalive.git"}, "bugs": {"url": "https://github.com/node-modules/agentkeepalive/issues"}, "keywords": ["http", "https", "agent", "keepalive", "agentkeepalive"], "dependencies": {"humanize-ms": "^1.2.1"}, "devDependencies": {"egg-bin": "^1.11.1", "eslint": "^4.19.1", "eslint-config-egg": "^6.0.0", "pedding": "^1.1.0"}, "engines": {"node": ">= 4.0.0"}, "publishConfig": {"tag": "latest-3"}, "author": "fengmk2 <<EMAIL>> (https://fengmk2.com)", "license": "MIT"}