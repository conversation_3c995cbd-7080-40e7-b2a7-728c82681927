<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="MarkdownProjectSettings">
    <PreviewSettings splitEditorLayout="FIRST" splitEditorPreview="NONE" useGrayscaleRendering="false" zoomFactor="1.0" maxImageWidth="0" showGitHubPageIfSynced="false" allowBrowsingInPreview="false" synchronizePreviewPosition="true" highlightPreviewType="LINE" highlightFadeOut="5" highlightOnTyping="true" synchronizeSourcePosition="true">
      <PanelProvider>
        <provider providerId="com.vladsch.idea.multimarkdown.editor.javafx.html.panel" providerName="JavaFX WebView" />
      </PanelProvider>
    </PreviewSettings>
    <ParserSettings>
      <PegdownExtensions>
        <option name="ABBREVIATIONS" value="false" />
        <option name="ANCHORLINKS" value="true" />
        <option name="ATXHEADERSPACE" value="false" />
        <option name="AUTOLINKS" value="true" />
        <option name="DEFINITIONS" value="true" />
        <option name="FENCED_CODE_BLOCKS" value="true" />
        <option name="FOOTNOTES" value="false" />
        <option name="FORCELISTITEMPARA" value="false" />
        <option name="HARDWRAPS" value="true" />
        <option name="QUOTES" value="false" />
        <option name="RELAXEDHRULES" value="true" />
        <option name="SMARTS" value="false" />
        <option name="STRIKETHROUGH" value="true" />
        <option name="SUPPRESS_HTML_BLOCKS" value="false" />
        <option name="SUPPRESS_INLINE_HTML" value="false" />
        <option name="TABLES" value="true" />
        <option name="TASKLISTITEMS" value="true" />
        <option name="TOC" value="false" />
        <option name="TRACE_PARSER" value="false" />
        <option name="WIKILINKS" value="true" />
      </PegdownExtensions>
      <ParserOptions>
        <option name="COMMONMARK_LISTS" value="false" />
        <option name="DUMMY" value="false" />
        <option name="EMOJI_SHORTCUTS" value="false" />
        <option name="FLEXMARK_FRONT_MATTER" value="false" />
        <option name="GFM_TABLE_RENDERING" value="true" />
        <option name="GITBOOK_URL_ENCODING" value="false" />
        <option name="GITHUB_EMOJI_URL" value="false" />
        <option name="GITHUB_LISTS" value="false" />
        <option name="GITHUB_WIKI_LINKS" value="true" />
        <option name="JEKYLL_FRONT_MATTER" value="false" />
        <option name="SIM_TOC_BLANK_LINE_SPACER" value="false" />
      </ParserOptions>
    </ParserSettings>
    <HtmlSettings headerTopEnabled="false" headerBottomEnabled="false" bodyTopEnabled="false" bodyBottomEnabled="false" embedUrlContent="false" addPageHeader="true">
      <GeneratorProvider>
        <provider providerId="com.vladsch.idea.multimarkdown.editor.javafx.html.generator" providerName="JavaFx HTML Generator" />
      </GeneratorProvider>
      <headerTop />
      <headerBottom />
      <bodyTop />
      <bodyBottom />
    </HtmlSettings>
    <CssSettings previewScheme="UI_SCHEME" cssUri="" isCssUriEnabled="false" isCssTextEnabled="false" isDynamicPageWidth="true">
      <StylesheetProvider>
        <provider providerId="com.vladsch.idea.multimarkdown.editor.javafx.html.css" providerName="Default JavaFx Stylesheet" />
      </StylesheetProvider>
      <ScriptProviders />
      <cssText />
    </CssSettings>
    <HtmlExportSettings updateOnSave="false" parentDir="$ProjectFileDir$" targetDir="$ProjectFileDir$" cssDir="" scriptDir="" plainHtml="false" imageDir="" copyLinkedImages="false" imageUniquifyType="0" targetExt="" useTargetExt="false" noCssNoScripts="false" linkToExportedHtml="true" exportOnSettingsChange="true" regenerateOnProjectOpen="false" />
    <LinkMapSettings>
      <textMaps />
    </LinkMapSettings>
  </component>
</project>