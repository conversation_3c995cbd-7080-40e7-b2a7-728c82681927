{"name": "jstoxml", "version": "2.2.9", "description": "Converts JavaScript/JSON to XML (for RSS, Podcasts, AMP, etc.)", "homepage": "http://github.com/davidcalhoun/jstoxml", "keywords": ["amp", "google amp", "json", "podcast", "rss", "sitemap", "site map", "xml"], "main": "dist/jstoxml.js", "author": "<PERSON> <<EMAIL>>", "license": "MIT", "repository": "git://github.com/davidcalhoun/jstoxml", "scripts": {"dist": "./dist.sh", "prepare": "./dist.sh", "test": "rm -rf dist && ./dist.sh && mocha test.js", "patch": "npm version patch && npm run npm-publish", "minor": "npm version minor && npm run npm-publish", "major": "npm version major && npm run npm-publish", "npm-publish": "npm publish && git push && git push --tags", "bump": "npm outdated & npm update && npm audit fix && npm test"}, "unpkg": "dist/jstoxml-min.js", "devDependencies": {"@babel/cli": "^7.16.0", "@babel/core": "^7.16.5", "@babel/plugin-transform-modules-umd": "^7.16.5", "@babel/preset-env": "^7.16.5", "babel-eslint": "^10.1.0", "eslint": "^8.5.0", "mocha": "^9.1.3", "prettier": "^2.5.1", "uglify-es": "^3.3.9"}}