{"name": "js-base64", "version": "2.6.4", "description": "Yet another Base64 transcoder in pure-JS", "main": "base64.js", "files": ["base64.js", "base64.min.js"], "directories": {"test": "test"}, "scripts": {"test": "mocha --require @babel/register", "minify": "uglifyjs base64.js > base64.min.js"}, "devDependencies": {"@babel/core": "^7.10.5", "@babel/preset-env": "^7.10.5", "@babel/register": "^7.10.5", "mocha": "^8.0.0", "uglify-js": "^3.10.0"}, "repository": {"type": "git", "url": "git://github.com/dankogai/js-base64.git"}, "keywords": ["base64"], "author": "<PERSON>", "license": "BSD-3-<PERSON><PERSON>", "readmeFilename": "README.md", "gitHead": "8bfa436f733bec60c95c720e1d720c28b43ae0b2"}