{"name": "osx-release", "version": "1.1.0", "description": "Get the name and version of a OS X release from the Darwin version. Example: 13.2.0 → {name: 'Mavericks', version: '10.9'}", "license": "MIT", "repository": "sindresorhus/osx-release", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "bin": "cli.js", "engines": {"node": ">=0.10.0"}, "scripts": {"test": "mocha"}, "files": ["index.js", "cli.js"], "keywords": ["cli-app", "cli", "bin", "os", "osx", "darwin", "operating", "system", "platform", "name", "title", "release", "version"], "dependencies": {"minimist": "^1.1.0"}, "devDependencies": {"mocha": "*"}}